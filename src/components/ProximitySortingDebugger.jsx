/**
 * Proximity Sorting Debugger
 * 
 * This module provides debugging utilities to help identify issues with the proximity sorting feature.
 * Use these functions in the browser console to test individual components.
 */

import { roomProximitySorter } from './RoomProximitySorter'
import { extractRoomCode } from './RoomCodeExtractor'
import { calculateAnnotationCentroid } from './AnnotationCentroid'

/**
 * Debug the complete proximity sorting workflow
 */
export const debugProximitySorting = async () => {
  console.log('🐛 Starting comprehensive proximity sorting debug...')
  
  const results = {
    step1_basicFunctionality: null,
    step2_roomCodeExtraction: null,
    step3_annotationCentroid: null,
    step4_proximitySort: null,
    summary: null
  }

  try {
    // Step 1: Test basic functionality
    console.log('📋 Step 1: Testing basic functionality...')
    results.step1_basicFunctionality = await roomProximitySorter.debugBasicFunctionality()
    
    // Step 2: Test room code extraction
    console.log('📋 Step 2: Testing room code extraction...')
    const testRoomNames = [
      'BIOWASTE 01.E.28',
      'Office 102.A.15',
      'Room 101',
      'Conference Room A',
      'Storage Area 3B'
    ]
    
    results.step2_roomCodeExtraction = testRoomNames.map(name => ({
      roomName: name,
      extractedCode: extractRoomCode(name)
    }))
    
    console.log('🏷️ Room code extraction results:', results.step2_roomCodeExtraction)
    
    // Step 3: Test annotation centroid calculation
    console.log('📋 Step 3: Testing annotation centroid calculation...')
    const testAnnotation = {
      id: 'test-rect',
      type: 'rectangle',
      x: 100,
      y: 100,
      width: 50,
      height: 50
    }
    
    try {
      results.step3_annotationCentroid = calculateAnnotationCentroid(testAnnotation)
      console.log('📐 Annotation centroid:', results.step3_annotationCentroid)
    } catch (error) {
      results.step3_annotationCentroid = { error: error.message }
      console.error('❌ Annotation centroid calculation failed:', error)
    }
    
    // Step 4: Test proximity sorting (if previous steps succeeded)
    console.log('📋 Step 4: Testing proximity sorting...')
    if (results.step1_basicFunctionality.success && results.step3_annotationCentroid && !results.step3_annotationCentroid.error) {
      try {
        console.log('🎯 Attempting proximity sort with test data...')
        const sortedRooms = await roomProximitySorter.sortRoomsByProximity(testAnnotation, testRoomNames)
        results.step4_proximitySort = {
          success: true,
          sortedRooms: sortedRooms.map(room => ({
            roomName: room.roomName,
            roomCode: room.roomCode,
            distance: room.distance,
            found: room.found
          }))
        }
        console.log('✅ Proximity sort completed:', results.step4_proximitySort)
      } catch (error) {
        results.step4_proximitySort = { error: error.message }
        console.error('❌ Proximity sort failed:', error)
      }
    } else {
      results.step4_proximitySort = { skipped: 'Previous steps failed' }
    }
    
    // Generate summary
    results.summary = generateDebugSummary(results)
    
  } catch (error) {
    console.error('❌ Debug process failed:', error)
    results.summary = { error: error.message }
  }
  
  console.log('🏁 Debug completed. Results:', results)
  return results
}

/**
 * Generate a summary of debug results
 */
const generateDebugSummary = (results) => {
  const summary = {
    overallStatus: 'unknown',
    issues: [],
    recommendations: []
  }
  
  // Check basic functionality
  if (!results.step1_basicFunctionality?.success) {
    summary.issues.push('PDF text extraction failed')
    summary.recommendations.push('Check if PDF page is properly loaded and accessible')
  }
  
  // Check room code extraction
  const validExtractions = results.step2_roomCodeExtraction?.filter(r => r.extractedCode !== null) || []
  if (validExtractions.length === 0) {
    summary.issues.push('No room codes could be extracted')
    summary.recommendations.push('Check room naming patterns and extraction logic')
  }
  
  // Check annotation centroid
  if (results.step3_annotationCentroid?.error) {
    summary.issues.push('Annotation centroid calculation failed')
    summary.recommendations.push('Check annotation object structure and centroid calculation logic')
  }
  
  // Check proximity sorting
  if (results.step4_proximitySort?.error) {
    summary.issues.push('Proximity sorting failed')
    summary.recommendations.push('Check coordinate transformation and distance calculation')
  } else if (results.step4_proximitySort?.skipped) {
    summary.issues.push('Proximity sorting was skipped due to previous failures')
  }
  
  // Determine overall status
  if (summary.issues.length === 0) {
    summary.overallStatus = 'success'
  } else if (results.step1_basicFunctionality?.success) {
    summary.overallStatus = 'partial'
  } else {
    summary.overallStatus = 'failed'
  }
  
  return summary
}

/**
 * Quick test for room code extraction only
 */
export const quickTestRoomCodes = (roomNames) => {
  console.log('🔍 Quick room code extraction test...')
  const results = roomNames.map(name => ({
    input: name,
    output: extractRoomCode(name)
  }))
  console.log('Results:', results)
  return results
}

/**
 * Quick test for annotation centroid calculation
 */
export const quickTestCentroid = (annotation) => {
  console.log('📐 Quick centroid calculation test...')
  try {
    const centroid = calculateAnnotationCentroid(annotation)
    console.log('Centroid:', centroid)
    return { success: true, centroid }
  } catch (error) {
    console.error('Error:', error)
    return { success: false, error: error.message }
  }
}

/**
 * Test if the proximity sorter is properly initialized
 */
export const testProximitySorterInit = () => {
  console.log('🔧 Testing proximity sorter initialization...')
  const stats = roomProximitySorter.getCacheStats()
  console.log('Stats:', stats)
  
  const isReady = stats.hasPdfPage && stats.hasCoordinateTransformer
  console.log('Is ready for sorting:', isReady)
  
  return { stats, isReady }
}

// Make debugging functions available globally
if (typeof window !== 'undefined') {
  window.debugProximitySorting = debugProximitySorting
  window.quickTestRoomCodes = quickTestRoomCodes
  window.quickTestCentroid = quickTestCentroid
  window.testProximitySorterInit = testProximitySorterInit
  
  console.log('🐛 Proximity sorting debugger loaded. Available functions:')
  console.log('  - debugProximitySorting() - Run complete debug test')
  console.log('  - quickTestRoomCodes(roomNames) - Test room code extraction')
  console.log('  - quickTestCentroid(annotation) - Test centroid calculation')
  console.log('  - testProximitySorterInit() - Check if sorter is initialized')
}
