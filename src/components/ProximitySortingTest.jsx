/**
 * Proximity Sorting Test Utilities
 * 
 * This module provides test functions to validate the proximity sorting feature
 * and help debug any issues with the implementation.
 */

import { extractRoomCode, testRoomCodeExtraction } from './RoomCodeExtractor'
import { calculateAnnotationCentroid, testCentroidCalculations } from './AnnotationCentroid'
import { createCoordinateTransformer, testCoordinateTransformations } from './CoordinateTransform'
import { roomProximitySorter } from './RoomProximitySorter'
import { pdfTextSearcher } from './PDFTextSearch'

/**
 * Test the complete proximity sorting workflow
 */
export class ProximitySortingTester {
  constructor() {
    this.testResults = []
  }

  /**
   * Run all proximity sorting tests
   * @returns {Object} Complete test results
   */
  async runAllTests() {
    console.log('🧪 Starting Proximity Sorting Tests...')
    
    const results = {
      roomCodeExtraction: this.testRoomCodeExtraction(),
      centroidCalculation: this.testCentroidCalculation(),
      coordinateTransformation: this.testCoordinateTransformation(),
      mockProximitySort: await this.testMockProximitySort(),
      summary: null
    }

    // Generate summary
    results.summary = this.generateTestSummary(results)
    
    console.log('✅ Proximity Sorting Tests Complete')
    console.log('📊 Test Summary:', results.summary)
    
    return results
  }

  /**
   * Test room code extraction functionality
   */
  testRoomCodeExtraction() {
    console.log('🔍 Testing Room Code Extraction...')
    
    const testCases = [
      { input: 'BIOWASTE 01.E.28', expected: '01.E.28' },
      { input: 'Office 102.A.15', expected: '102.A.15' },
      { input: 'Room 101', expected: '101' },
      { input: 'Conference Room A', expected: null },
      { input: '01.E.28', expected: '01.E.28' },
      { input: 'Storage Area 3B', expected: '3B' },
      { input: 'Corridor', expected: null },
      { input: 'Lab-205', expected: 'Lab-205' },
      { input: 'WC.01.02', expected: 'WC.01.02' }
    ]

    const results = testCases.map(testCase => {
      const result = extractRoomCode(testCase.input)
      const passed = result === testCase.expected
      
      if (!passed) {
        console.warn(`❌ Room code extraction failed for "${testCase.input}": expected "${testCase.expected}", got "${result}"`)
      }
      
      return {
        input: testCase.input,
        expected: testCase.expected,
        actual: result,
        passed
      }
    })

    const passedCount = results.filter(r => r.passed).length
    console.log(`✅ Room Code Extraction: ${passedCount}/${results.length} tests passed`)
    
    return {
      passed: passedCount,
      total: results.length,
      results
    }
  }

  /**
   * Test centroid calculation functionality
   */
  testCentroidCalculation() {
    console.log('📐 Testing Centroid Calculation...')
    
    const testAnnotations = [
      {
        id: 'rect1',
        type: 'rectangle',
        x: 100,
        y: 200,
        width: 50,
        height: 30,
        expectedCentroid: { x: 125, y: 215 }
      },
      {
        id: 'poly1',
        type: 'polygon',
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
          { x: 0, y: 100 }
        ],
        expectedCentroid: { x: 50, y: 50 }
      }
    ]

    const results = testAnnotations.map(annotation => {
      try {
        const centroid = calculateAnnotationCentroid(annotation)
        const expectedCentroid = annotation.expectedCentroid
        
        const deltaX = Math.abs(centroid.x - expectedCentroid.x)
        const deltaY = Math.abs(centroid.y - expectedCentroid.y)
        const tolerance = 1 // Allow 1 unit tolerance
        
        const passed = deltaX <= tolerance && deltaY <= tolerance
        
        if (!passed) {
          console.warn(`❌ Centroid calculation failed for ${annotation.id}: expected (${expectedCentroid.x}, ${expectedCentroid.y}), got (${centroid.x}, ${centroid.y})`)
        }
        
        return {
          annotationId: annotation.id,
          type: annotation.type,
          expected: expectedCentroid,
          actual: centroid,
          delta: { x: deltaX, y: deltaY },
          passed
        }
      } catch (error) {
        console.error(`❌ Error calculating centroid for ${annotation.id}:`, error)
        return {
          annotationId: annotation.id,
          type: annotation.type,
          error: error.message,
          passed: false
        }
      }
    })

    const passedCount = results.filter(r => r.passed).length
    console.log(`✅ Centroid Calculation: ${passedCount}/${results.length} tests passed`)
    
    return {
      passed: passedCount,
      total: results.length,
      results
    }
  }

  /**
   * Test coordinate transformation functionality
   */
  testCoordinateTransformation() {
    console.log('🗺️ Testing Coordinate Transformation...')
    
    const pdfDimensions = { width: 612, height: 792 }
    const canvasDimensions = { width: 800, height: 600 }
    
    const transformer = createCoordinateTransformer(pdfDimensions, canvasDimensions)
    
    const testPoints = [
      { canvas: { x: 0, y: 0 }, description: 'Top-left corner' },
      { canvas: { x: 400, y: 300 }, description: 'Center' },
      { canvas: { x: 800, y: 600 }, description: 'Bottom-right corner' }
    ]

    const results = testPoints.map(test => {
      const pdfCoords = transformer.canvasToPdf(test.canvas.x, test.canvas.y)
      const backToCanvas = transformer.pdfToCanvas(pdfCoords.x, pdfCoords.y)
      
      const errorX = Math.abs(test.canvas.x - backToCanvas.x)
      const errorY = Math.abs(test.canvas.y - backToCanvas.y)
      const tolerance = 0.1 // Allow small floating point errors
      
      const passed = errorX <= tolerance && errorY <= tolerance
      
      if (!passed) {
        console.warn(`❌ Coordinate transformation failed for ${test.description}: round-trip error (${errorX}, ${errorY})`)
      }
      
      return {
        description: test.description,
        canvas: test.canvas,
        pdf: pdfCoords,
        backToCanvas: backToCanvas,
        roundTripError: { x: errorX, y: errorY },
        passed
      }
    })

    const passedCount = results.filter(r => r.passed).length
    console.log(`✅ Coordinate Transformation: ${passedCount}/${results.length} tests passed`)
    
    return {
      passed: passedCount,
      total: results.length,
      results,
      transformerInfo: transformer.getCoordinateSystemInfo()
    }
  }

  /**
   * Test proximity sorting with mock data
   */
  async testMockProximitySort() {
    console.log('🎯 Testing Mock Proximity Sorting...')
    
    // Create mock annotation
    const mockAnnotation = {
      id: 'test-rect',
      type: 'rectangle',
      x: 100,
      y: 100,
      width: 50,
      height: 50
    }

    // Create mock room names
    const mockRoomNames = [
      'OFFICE 01.E.28',
      'LAB 02.A.15',
      'STORAGE 03.B.22',
      'MEETING ROOM 01.C.10',
      'CORRIDOR'
    ]

    try {
      // Test room code extraction
      const roomCodes = mockRoomNames.map(name => ({
        name,
        code: extractRoomCode(name)
      }))

      console.log('🏷️ Extracted room codes:', roomCodes)

      // Test centroid calculation
      const centroid = calculateAnnotationCentroid(mockAnnotation)
      console.log('📍 Annotation centroid:', centroid)

      // Since we don't have a real PDF page, we'll test the sorting logic structure
      const mockSortResults = mockRoomNames.map((roomName, index) => ({
        roomName,
        roomCode: extractRoomCode(roomName),
        distance: Math.random() * 100 + index * 10, // Mock distances
        found: extractRoomCode(roomName) !== null
      })).sort((a, b) => {
        if (a.found && !b.found) return -1
        if (!a.found && b.found) return 1
        return a.distance - b.distance
      })

      console.log('📊 Mock sort results:', mockSortResults)

      return {
        passed: true,
        annotation: mockAnnotation,
        centroid,
        roomNames: mockRoomNames,
        roomCodes,
        sortResults: mockSortResults
      }
    } catch (error) {
      console.error('❌ Mock proximity sort test failed:', error)
      return {
        passed: false,
        error: error.message
      }
    }
  }

  /**
   * Generate test summary
   */
  generateTestSummary(results) {
    const totalTests = Object.values(results)
      .filter(r => r && typeof r === 'object' && 'passed' in r)
      .reduce((sum, r) => sum + (r.total || 1), 0)
    
    const passedTests = Object.values(results)
      .filter(r => r && typeof r === 'object' && 'passed' in r)
      .reduce((sum, r) => sum + (r.passed || (r.passed === true ? 1 : 0)), 0)

    const overallPassed = passedTests === totalTests

    return {
      totalTests,
      passedTests,
      failedTests: totalTests - passedTests,
      overallPassed,
      successRate: totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) + '%' : '0%'
    }
  }
}

// Create a singleton instance for testing
export const proximitySortingTester = new ProximitySortingTester()

/**
 * Quick test function for console usage
 */
export const runProximitySortingTests = async () => {
  return proximitySortingTester.runAllTests()
}

/**
 * Test individual components
 */
export const testComponents = {
  roomCodeExtraction: testRoomCodeExtraction,
  centroidCalculation: testCentroidCalculations,
  coordinateTransformation: testCoordinateTransformations
}
