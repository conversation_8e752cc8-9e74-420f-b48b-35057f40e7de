import React, { useState, useEffect, useRef } from 'react'
import { roomProximitySorter } from './RoomProximitySorter'

const RoomNameDropdown = ({
  roomNames,
  position,
  onSelectRoom,
  onCancel,
  isVisible,
  currentAnnotations = [],
  // New props for proximity sorting
  currentAnnotation = null,
  pdfPage = null,
  pageIndex = null,
  pdfDimensions = null,
  canvasDimensions = null,
  enableProximitySort = false
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [customRoomName, setCustomRoomName] = useState('')
  const [showCustomInput, setShowCustomInput] = useState(false)
  const [sortedRoomNames, setSortedRoomNames] = useState([])
  const [isProximitySorting, setIsProximitySorting] = useState(false)
  const [proximitySortEnabled, setProximitySortEnabled] = useState(false)
  const dropdownRef = useRef(null)
  const searchInputRef = useRef(null)
  const customInputRef = useRef(null)

  // Get used room names from current annotations
  const usedRoomNames = new Set(
    currentAnnotations
      .filter(annotation => annotation.roomName || annotation.label)
      .map(annotation => annotation.roomName || annotation.label)
  )

  // Initialize proximity sorting when annotation and PDF data are available
  useEffect(() => {
    const canEnableProximitySort = enableProximitySort &&
                                   currentAnnotation &&
                                   pdfPage &&
                                   pageIndex !== null &&
                                   pdfDimensions &&
                                   canvasDimensions

    setProximitySortEnabled(canEnableProximitySort)

    if (canEnableProximitySort) {
      // Initialize the proximity sorter
      roomProximitySorter.initialize(pdfPage, pageIndex, pdfDimensions, canvasDimensions)
    }
  }, [enableProximitySort, currentAnnotation, pdfPage, pageIndex, pdfDimensions, canvasDimensions])

  // Sort rooms by proximity when annotation or room names change
  useEffect(() => {
    const performProximitySort = async () => {
      if (!proximitySortEnabled || !currentAnnotation || roomNames.length === 0) {
        setSortedRoomNames([])
        return
      }

      setIsProximitySorting(true)

      try {
        const sortedNames = await roomProximitySorter.getSortedRoomNames(currentAnnotation, roomNames)
        setSortedRoomNames(sortedNames)
      } catch (error) {
        console.error('Error performing proximity sort in dropdown:', error)
        setSortedRoomNames([])
      } finally {
        setIsProximitySorting(false)
      }
    }

    performProximitySort()
  }, [proximitySortEnabled, currentAnnotation, roomNames])

  // Use sorted room names if proximity sorting is enabled, otherwise use original order
  const baseRoomNames = proximitySortEnabled && sortedRoomNames.length > 0 ? sortedRoomNames : roomNames

  // Filter room names based on search term and exclude already used names
  const filteredRoomNames = baseRoomNames
    .filter(name => !usedRoomNames.has(name)) // Exclude used room names
    .filter(name => name.toLowerCase().includes(searchTerm.toLowerCase())) // Apply search filter

  // Reset selection when filtered list changes
  useEffect(() => {
    setSelectedIndex(0)
  }, [searchTerm])

  // Focus appropriate input when dropdown becomes visible
  useEffect(() => {
    if (isVisible) {
      if (showCustomInput && customInputRef.current) {
        customInputRef.current.focus()
      } else if (searchInputRef.current) {
        searchInputRef.current.focus()
      }
    }
  }, [isVisible, showCustomInput])

  // Handle custom room name submission
  const handleCustomRoomSubmit = () => {
    if (customRoomName.trim()) {
      onSelectRoom(customRoomName.trim())
    }
  }

  // Toggle between search and custom input modes
  const toggleCustomInput = () => {
    setShowCustomInput(!showCustomInput)
    setSearchTerm('')
    setCustomRoomName('')
    setSelectedIndex(0)
  }

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isVisible) return

      switch (event.key) {
        case 'ArrowDown':
          if (!showCustomInput) {
            event.preventDefault()
            setSelectedIndex(prev =>
              prev < filteredRoomNames.length - 1 ? prev + 1 : prev
            )
          }
          break
        case 'ArrowUp':
          if (!showCustomInput) {
            event.preventDefault()
            setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
          }
          break
        case 'Enter':
          event.preventDefault()
          if (showCustomInput) {
            handleCustomRoomSubmit()
          } else if (filteredRoomNames.length > 0) {
            onSelectRoom(filteredRoomNames[selectedIndex])
          }
          break
        case 'Escape':
          event.preventDefault()
          onCancel()
          break
        default:
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVisible, selectedIndex, filteredRoomNames, onSelectRoom, onCancel, showCustomInput, handleCustomRoomSubmit])

  // Handle click outside to close dropdown (with delay to prevent immediate closure)
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onCancel()
      }
    }

    if (isVisible) {
      // Add a small delay to prevent the current mouse event from closing the dropdown
      const timer = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside)
      }, 100)

      return () => {
        clearTimeout(timer)
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [isVisible, onCancel])

  if (!isVisible || roomNames.length === 0) {
    return null
  }

  return (
    <div
      ref={dropdownRef}
      className="room-name-dropdown"
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 1000
      }}
    >
      <div className="room-dropdown-header">
        <h4>
          Assign Room Name
          {proximitySortEnabled && currentAnnotation && (
            <span className="proximity-indicator">
              {isProximitySorting ? ' 🔄' : ' 📍'}
            </span>
          )}
        </h4>
        {proximitySortEnabled && currentAnnotation && (
          <div className="proximity-info">
            {isProximitySorting ? (
              <span className="sorting-text">Sorting by proximity...</span>
            ) : (
              <span className="sorted-text">Sorted by distance from annotation</span>
            )}
          </div>
        )}
        <div className="input-mode-toggle">
          <button
            className={`mode-toggle-btn ${!showCustomInput ? 'active' : ''}`}
            onClick={() => !showCustomInput || toggleCustomInput()}
            title="Search existing rooms"
          >
            📋 Search
          </button>
          <button
            className={`mode-toggle-btn ${showCustomInput ? 'active' : ''}`}
            onClick={() => showCustomInput || toggleCustomInput()}
            title="Type custom room name"
          >
            ✏️ Custom
          </button>
        </div>

        {showCustomInput ? (
          <input
            ref={customInputRef}
            type="text"
            placeholder="Type custom room name..."
            value={customRoomName}
            onChange={(e) => setCustomRoomName(e.target.value)}
            className="room-custom-input"
          />
        ) : (
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search room names..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="room-search-input"
          />
        )}
      </div>
      
      {showCustomInput ? (
        <div className="custom-room-preview">
          {customRoomName.trim() ? (
            <div className="custom-room-display">
              <span className="custom-room-label">Custom Room:</span>
              <span className="custom-room-name">"{customRoomName.trim()}"</span>
            </div>
          ) : (
            <div className="custom-room-hint">
              Type your custom room name above and press Enter or click Add
            </div>
          )}
        </div>
      ) : (
        <div className="room-dropdown-list">
          {filteredRoomNames.length > 0 ? (
            filteredRoomNames.map((roomName, index) => (
              <div
                key={roomName}
                className={`room-dropdown-item ${index === selectedIndex ? 'selected' : ''}`}
                onClick={() => onSelectRoom(roomName)}
                onMouseEnter={() => setSelectedIndex(index)}
              >
                {roomName}
              </div>
            ))
          ) : (
            <div className="room-dropdown-item no-results">
              No rooms found matching "{searchTerm}"
              <button
                className="switch-to-custom-btn text-black"
                onClick={toggleCustomInput}
              >
                Type custom name instead
              </button>
            </div>
          )}
        </div>
      )}
      
      <div className="room-dropdown-footer">
        <button onClick={onCancel} className="cancel-button">
          Cancel (Esc)
        </button>

        {showCustomInput ? (
          <button
            onClick={handleCustomRoomSubmit}
            className="add-custom-button"
            disabled={!customRoomName.trim()}
            title={customRoomName.trim() ? `Add "${customRoomName.trim()}"` : 'Enter a room name first'}
          >
            Add Custom Room (Enter)
          </button>
        ) : (
          <>
            {filteredRoomNames.length > 0 && (
              <button
                onClick={() => onSelectRoom(filteredRoomNames[selectedIndex])}
                className="select-button"
              >
                Select (Enter)
              </button>
            )}
            <button
              onClick={toggleCustomInput}
              className="custom-mode-button"
              title="Switch to custom room name input"
            >
              ✏️ Custom Name
            </button>
          </>
        )}
      </div>
    </div>
  )
}

export default RoomNameDropdown
