/**
 * Room Code Extraction Utilities
 * 
 * This module provides functionality to extract room codes from room names
 * using various patterns and handle edge cases.
 */

/**
 * Extract room code from room name using the pattern: "BIOWASTE 01.E.28" -> "01.E.28"
 * @param {string} roomName - Full room name
 * @returns {string|null} Extracted room code or null if no valid pattern found
 */
export const extractRoomCode = (roomName) => {
  if (!roomName || typeof roomName !== 'string') {
    return null
  }

  const trimmedName = roomName.trim()
  if (!trimmedName) {
    return null
  }

  // Pattern 1: Standard format - extract part after last space
  // Example: "BIOWASTE 01.E.28" -> "01.E.28"
  const lastSpaceIndex = trimmedName.lastIndexOf(' ')
  if (lastSpaceIndex !== -1 && lastSpaceIndex < trimmedName.length - 1) {
    const potentialCode = trimmedName.substring(lastSpaceIndex + 1)
    
    // Validate that the extracted part looks like a room code
    // Room codes typically contain numbers and may contain dots, letters, hyphens
    if (isValidRoomCode(potentialCode)) {
      return potentialCode
    }
  }

  // Pattern 2: If no space found, check if the entire name is a room code
  if (isValidRoomCode(trimmedName)) {
    return trimmedName
  }

  // Pattern 3: Try to extract room code from common patterns
  const extractedCode = extractFromCommonPatterns(trimmedName)
  if (extractedCode) {
    return extractedCode
  }

  // If no valid room code found, return null
  return null
}

/**
 * Validate if a string looks like a valid room code
 * @param {string} code - Potential room code
 * @returns {boolean} True if it looks like a valid room code
 */
const isValidRoomCode = (code) => {
  if (!code || code.length < 2) {
    return false
  }

  // Room codes should contain at least one number
  const hasNumber = /\d/.test(code)
  
  // Room codes should not contain only letters (likely part of description)
  const isOnlyLetters = /^[a-zA-Z]+$/.test(code)
  
  // Room codes should not be too long (likely a description)
  const isTooLong = code.length > 20
  
  // Room codes should not contain common words
  const commonWords = ['room', 'office', 'hall', 'corridor', 'area', 'zone', 'space', 'floor']
  const containsCommonWord = commonWords.some(word => 
    code.toLowerCase().includes(word.toLowerCase())
  )

  return hasNumber && !isOnlyLetters && !isTooLong && !containsCommonWord
}

/**
 * Extract room codes from common naming patterns
 * @param {string} roomName - Full room name
 * @returns {string|null} Extracted room code or null
 */
const extractFromCommonPatterns = (roomName) => {
  const name = roomName.toLowerCase()

  // Pattern: "Room 101" -> "101"
  const roomPattern = /room\s+([a-zA-Z0-9.-]+)/i
  let match = roomName.match(roomPattern)
  if (match && isValidRoomCode(match[1])) {
    return match[1]
  }

  // Pattern: "Office 01.E.28" -> "01.E.28"
  const officePattern = /office\s+([a-zA-Z0-9.-]+)/i
  match = roomName.match(officePattern)
  if (match && isValidRoomCode(match[1])) {
    return match[1]
  }

  // Pattern: Numbers with dots/letters at the end
  const numberPattern = /([0-9]+[a-zA-Z0-9.-]*[a-zA-Z0-9])$/
  match = roomName.match(numberPattern)
  if (match && isValidRoomCode(match[1])) {
    return match[1]
  }

  // Pattern: Extract alphanumeric codes with dots/hyphens
  const codePattern = /([a-zA-Z0-9]+[.-][a-zA-Z0-9.-]+)/
  match = roomName.match(codePattern)
  if (match && isValidRoomCode(match[1])) {
    return match[1]
  }

  return null
}

/**
 * Extract room codes from multiple room names
 * @param {Array<string>} roomNames - Array of room names
 * @returns {Array<{roomName: string, roomCode: string|null}>} Array of room name to code mappings
 */
export const extractRoomCodes = (roomNames) => {
  if (!Array.isArray(roomNames)) {
    return []
  }

  return roomNames.map(roomName => ({
    roomName,
    roomCode: extractRoomCode(roomName)
  }))
}

/**
 * Filter room names that have valid extractable room codes
 * @param {Array<string>} roomNames - Array of room names
 * @returns {Array<{roomName: string, roomCode: string}>} Array of rooms with valid codes
 */
export const filterRoomsWithCodes = (roomNames) => {
  return extractRoomCodes(roomNames)
    .filter(item => item.roomCode !== null)
    .map(item => ({
      roomName: item.roomName,
      roomCode: item.roomCode
    }))
}

/**
 * Create a mapping from room codes to room names
 * @param {Array<string>} roomNames - Array of room names
 * @returns {Map<string, string>} Map from room code to room name
 */
export const createRoomCodeMap = (roomNames) => {
  const map = new Map()
  const roomsWithCodes = filterRoomsWithCodes(roomNames)
  
  roomsWithCodes.forEach(({ roomName, roomCode }) => {
    map.set(roomCode, roomName)
  })
  
  return map
}

/**
 * Test the room code extraction with sample data
 * @returns {Object} Test results
 */
export const testRoomCodeExtraction = () => {
  const testCases = [
    'BIOWASTE 01.E.28',
    'Office 102.A.15',
    'Room 101',
    'Conference Room A',
    '01.E.28',
    'Storage Area 3B',
    'Corridor',
    'Main Hall',
    'Lab-205',
    'WC.01.02',
    'Meeting Room 1.2.3'
  ]

  const results = testCases.map(testCase => ({
    input: testCase,
    output: extractRoomCode(testCase)
  }))

  console.log('Room Code Extraction Test Results:', results)
  return results
}

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  window.testRoomCodeExtraction = testRoomCodeExtraction
  window.extractRoomCode = extractRoomCode
}
