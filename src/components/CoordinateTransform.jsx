/**
 * Coordinate System Transformation Utilities
 * 
 * This module provides functionality to convert between different coordinate systems:
 * - Canvas coordinates (used for annotations, top-left origin)
 * - PDF coordinates (used for text search, bottom-left origin for rendering, top-left for text)
 * - Screen coordinates (browser viewport)
 */

/**
 * Coordinate system information and transformation utilities
 */
export class CoordinateTransformer {
  constructor(pdfDimensions, canvasDimensions) {
    this.pdfDimensions = pdfDimensions || { width: 612, height: 792 }
    this.canvasDimensions = canvasDimensions || { width: 800, height: 600 }
    this.scaleX = this.canvasDimensions.width / this.pdfDimensions.width
    this.scaleY = this.canvasDimensions.height / this.pdfDimensions.height
  }

  /**
   * Update dimensions when PDF or canvas changes
   * @param {Object} pdfDimensions - PDF dimensions {width, height}
   * @param {Object} canvasDimensions - Canvas dimensions {width, height}
   */
  updateDimensions(pdfDimensions, canvasDimensions) {
    this.pdfDimensions = pdfDimensions
    this.canvasDimensions = canvasDimensions
    this.scaleX = this.canvasDimensions.width / this.pdfDimensions.width
    this.scaleY = this.canvasDimensions.height / this.pdfDimensions.height
  }

  /**
   * Convert canvas coordinates to PDF coordinates
   * Canvas: top-left origin, scaled to display size
   * PDF: top-left origin for text coordinates, original PDF dimensions
   * @param {number} canvasX - Canvas X coordinate
   * @param {number} canvasY - Canvas Y coordinate
   * @returns {Object} PDF coordinates {x, y}
   */
  canvasToPdf(canvasX, canvasY) {
    return {
      x: canvasX / this.scaleX,
      y: canvasY / this.scaleY
    }
  }

  /**
   * Convert PDF coordinates to canvas coordinates
   * @param {number} pdfX - PDF X coordinate
   * @param {number} pdfY - PDF Y coordinate
   * @returns {Object} Canvas coordinates {x, y}
   */
  pdfToCanvas(pdfX, pdfY) {
    return {
      x: pdfX * this.scaleX,
      y: pdfY * this.scaleY
    }
  }

  /**
   * Convert PDF text coordinates to PDF rendering coordinates
   * PDF text uses top-left origin, PDF rendering uses bottom-left origin
   * @param {number} textX - PDF text X coordinate
   * @param {number} textY - PDF text Y coordinate
   * @returns {Object} PDF rendering coordinates {x, y}
   */
  pdfTextToPdfRender(textX, textY) {
    return {
      x: textX,
      y: this.pdfDimensions.height - textY
    }
  }

  /**
   * Convert PDF rendering coordinates to PDF text coordinates
   * @param {number} renderX - PDF rendering X coordinate
   * @param {number} renderY - PDF rendering Y coordinate
   * @returns {Object} PDF text coordinates {x, y}
   */
  pdfRenderToPdfText(renderX, renderY) {
    return {
      x: renderX,
      y: this.pdfDimensions.height - renderY
    }
  }

  /**
   * Convert canvas coordinates to PDF text coordinates
   * This is the key function for comparing annotation positions with text positions
   * @param {number} canvasX - Canvas X coordinate
   * @param {number} canvasY - Canvas Y coordinate
   * @returns {Object} PDF text coordinates {x, y}
   */
  canvasToPdfText(canvasX, canvasY) {
    // First convert to PDF coordinates (same coordinate system as PDF text)
    return this.canvasToPdf(canvasX, canvasY)
  }

  /**
   * Convert PDF text coordinates to canvas coordinates
   * @param {number} textX - PDF text X coordinate
   * @param {number} textY - PDF text Y coordinate
   * @returns {Object} Canvas coordinates {x, y}
   */
  pdfTextToCanvas(textX, textY) {
    // PDF text coordinates are already in top-left origin system
    return this.pdfToCanvas(textX, textY)
  }

  /**
   * Calculate Euclidean distance between two points
   * @param {Object} point1 - First point {x, y}
   * @param {Object} point2 - Second point {x, y}
   * @returns {number} Distance between points
   */
  calculateDistance(point1, point2) {
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * Calculate distance between annotation centroid and room text coordinates
   * Handles coordinate system conversion automatically
   * @param {Object} annotationCentroid - Annotation centroid in canvas coordinates {x, y}
   * @param {Object} roomTextCoords - Room text coordinates from PDF {x, y}
   * @returns {number} Distance in PDF coordinate units
   */
  calculateAnnotationToRoomDistance(annotationCentroid, roomTextCoords) {
    // Convert annotation centroid from canvas to PDF text coordinates
    const pdfAnnotationCoords = this.canvasToPdfText(annotationCentroid.x, annotationCentroid.y)
    
    // Calculate distance in PDF coordinate space
    return this.calculateDistance(pdfAnnotationCoords, roomTextCoords)
  }

  /**
   * Get coordinate system information for debugging
   * @returns {Object} Coordinate system info
   */
  getCoordinateSystemInfo() {
    return {
      pdfDimensions: this.pdfDimensions,
      canvasDimensions: this.canvasDimensions,
      scaleX: this.scaleX,
      scaleY: this.scaleY,
      coordinateSystems: {
        canvas: 'Top-left origin, scaled to display size',
        pdfText: 'Top-left origin, original PDF dimensions',
        pdfRender: 'Bottom-left origin, original PDF dimensions'
      }
    }
  }
}

/**
 * Create a coordinate transformer instance
 * @param {Object} pdfDimensions - PDF dimensions {width, height}
 * @param {Object} canvasDimensions - Canvas dimensions {width, height}
 * @returns {CoordinateTransformer} Transformer instance
 */
export const createCoordinateTransformer = (pdfDimensions, canvasDimensions) => {
  return new CoordinateTransformer(pdfDimensions, canvasDimensions)
}

/**
 * Utility functions for coordinate validation
 */
export const validateCoordinates = {
  /**
   * Check if coordinates are valid numbers
   * @param {Object} coords - Coordinates {x, y}
   * @returns {boolean} True if valid
   */
  isValid(coords) {
    return coords && 
           typeof coords.x === 'number' && 
           typeof coords.y === 'number' &&
           !isNaN(coords.x) && 
           !isNaN(coords.y) &&
           isFinite(coords.x) && 
           isFinite(coords.y)
  },

  /**
   * Check if coordinates are within bounds
   * @param {Object} coords - Coordinates {x, y}
   * @param {Object} bounds - Bounds {width, height}
   * @returns {boolean} True if within bounds
   */
  isWithinBounds(coords, bounds) {
    return this.isValid(coords) &&
           coords.x >= 0 && coords.x <= bounds.width &&
           coords.y >= 0 && coords.y <= bounds.height
  },

  /**
   * Clamp coordinates to bounds
   * @param {Object} coords - Coordinates {x, y}
   * @param {Object} bounds - Bounds {width, height}
   * @returns {Object} Clamped coordinates {x, y}
   */
  clampToBounds(coords, bounds) {
    if (!this.isValid(coords)) {
      return { x: 0, y: 0 }
    }
    
    return {
      x: Math.max(0, Math.min(bounds.width, coords.x)),
      y: Math.max(0, Math.min(bounds.height, coords.y))
    }
  }
}

/**
 * Test coordinate transformations with sample data
 * @param {Object} pdfDimensions - PDF dimensions for testing
 * @param {Object} canvasDimensions - Canvas dimensions for testing
 * @returns {Object} Test results
 */
export const testCoordinateTransformations = (
  pdfDimensions = { width: 612, height: 792 },
  canvasDimensions = { width: 800, height: 600 }
) => {
  const transformer = new CoordinateTransformer(pdfDimensions, canvasDimensions)
  
  const testPoints = [
    { canvas: { x: 0, y: 0 }, description: 'Top-left corner' },
    { canvas: { x: 400, y: 300 }, description: 'Center' },
    { canvas: { x: 800, y: 600 }, description: 'Bottom-right corner' }
  ]

  const results = testPoints.map(test => {
    const pdfCoords = transformer.canvasToPdf(test.canvas.x, test.canvas.y)
    const backToCanvas = transformer.pdfToCanvas(pdfCoords.x, pdfCoords.y)
    
    return {
      description: test.description,
      canvas: test.canvas,
      pdf: pdfCoords,
      backToCanvas: backToCanvas,
      roundTripError: {
        x: Math.abs(test.canvas.x - backToCanvas.x),
        y: Math.abs(test.canvas.y - backToCanvas.y)
      }
    }
  })

  console.log('Coordinate Transformation Test Results:', results)
  console.log('Coordinate System Info:', transformer.getCoordinateSystemInfo())
  
  return results
}
