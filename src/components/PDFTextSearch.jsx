import * as pdfjsLib from 'pdfjs-dist'

/**
 * PDF Text Search and Coordinate Extraction Utilities
 * 
 * This module provides functionality to search for text within PDF documents
 * and extract the coordinates of found text using PDF.js text content extraction.
 */

export class PDFTextSearcher {
  constructor() {
    this.textContentCache = new Map() // Cache text content by PDF page
    this.coordinateCache = new Map()  // Cache room coordinates
  }

  /**
   * Extract text content with coordinates from a PDF page
   * @param {Object} pdfPage - PDF.js page object
   * @param {number} pageIndex - Page index for caching
   * @returns {Promise<Array>} Array of text items with coordinates
   */
  async extractTextContent(pdfPage, pageIndex) {
    const cacheKey = `page_${pageIndex}`
    console.log(`📄 PDFTextSearcher: extractTextContent called for page ${pageIndex}, cache key: ${cacheKey}`)

    // Check cache first
    if (this.textContentCache.has(cacheKey)) {
      const cachedItems = this.textContentCache.get(cacheKey)
      console.log(`✅ PDFTextSearcher: Found cached text content for page ${pageIndex}: ${cachedItems.length} items`)
      return cachedItems
    }

    try {
      console.log(`📖 PDFTextSearcher: Calling pdfPage.getTextContent() for page ${pageIndex}`)
      const textContent = await pdfPage.getTextContent()
      console.log(`📖 PDFTextSearcher: Got text content with ${textContent.items.length} items`)

      const textItems = textContent.items.map(item => ({
        str: item.str,
        x: item.transform[4], // X coordinate from transform matrix
        y: item.transform[5], // Y coordinate from transform matrix
        width: item.width,
        height: item.height,
        fontName: item.fontName,
        fontSize: item.transform[0], // Font size from transform matrix
        transform: item.transform
      }))

      console.log(`📄 PDFTextSearcher: Processed ${textItems.length} text items for page ${pageIndex}`)
      console.log(`📄 PDFTextSearcher: Sample text items:`, textItems.slice(0, 5).map(item => ({ str: item.str, x: item.x, y: item.y })))

      // Cache the result
      this.textContentCache.set(cacheKey, textItems)
      return textItems
    } catch (error) {
      console.error(`❌ PDFTextSearcher: Error extracting text content for page ${pageIndex}:`, error)
      return []
    }
  }

  /**
   * Search for a specific text string in PDF text items
   * @param {Array} textItems - Array of text items from extractTextContent
   * @param {string} searchText - Text to search for
   * @param {boolean} caseSensitive - Whether search should be case sensitive
   * @returns {Array} Array of matching text items with coordinates
   */
  searchTextInItems(textItems, searchText, caseSensitive = false) {
    if (!searchText || !textItems) return []

    const searchTerm = caseSensitive ? searchText : searchText.toLowerCase()
    
    return textItems.filter(item => {
      const itemText = caseSensitive ? item.str : item.str.toLowerCase()
      return itemText.includes(searchTerm)
    })
  }

  /**
   * Search for text coordinates in a PDF page
   * @param {Object} pdfPage - PDF.js page object
   * @param {string} searchText - Text to search for
   * @param {number} pageIndex - Page index for caching
   * @param {boolean} caseSensitive - Whether search should be case sensitive
   * @returns {Promise<Array>} Array of coordinate objects {x, y, width, height, text}
   */
  async searchTextCoordinates(pdfPage, searchText, pageIndex, caseSensitive = false) {
    console.log(`🔍 PDFTextSearcher: searchTextCoordinates called for "${searchText}" on page ${pageIndex}`)

    try {
      console.log(`📄 PDFTextSearcher: Extracting text content from page ${pageIndex}`)
      const textItems = await this.extractTextContent(pdfPage, pageIndex)
      console.log(`📄 PDFTextSearcher: Extracted ${textItems.length} text items from page ${pageIndex}`)

      console.log(`🔎 PDFTextSearcher: Searching for "${searchText}" in text items (case sensitive: ${caseSensitive})`)
      const matches = this.searchTextInItems(textItems, searchText, caseSensitive)
      console.log(`📊 PDFTextSearcher: Found ${matches.length} matches for "${searchText}"`)

      const results = matches.map(item => ({
        x: item.x,
        y: item.y,
        width: item.width,
        height: item.height,
        text: item.str,
        fontSize: item.fontSize,
        fontName: item.fontName,
        // Calculate center point for easier distance calculations
        centerX: item.x + (item.width / 2),
        centerY: item.y + (item.height / 2)
      }))

      console.log(`✅ PDFTextSearcher: Returning ${results.length} coordinate results for "${searchText}"`)
      return results
    } catch (error) {
      console.error(`❌ PDFTextSearcher: Error searching text coordinates for "${searchText}":`, error)
      return []
    }
  }

  /**
   * Search for multiple text strings in a PDF page
   * @param {Object} pdfPage - PDF.js page object
   * @param {Array} searchTexts - Array of text strings to search for
   * @param {number} pageIndex - Page index for caching
   * @param {boolean} caseSensitive - Whether search should be case sensitive
   * @returns {Promise<Object>} Object mapping search text to coordinate arrays
   */
  async searchMultipleTexts(pdfPage, searchTexts, pageIndex, caseSensitive = false) {
    const textItems = await this.extractTextContent(pdfPage, pageIndex)
    const results = {}

    for (const searchText of searchTexts) {
      const matches = this.searchTextInItems(textItems, searchText, caseSensitive)
      results[searchText] = matches.map(item => ({
        x: item.x,
        y: item.y,
        width: item.width,
        height: item.height,
        text: item.str,
        fontSize: item.fontSize,
        fontName: item.fontName,
        centerX: item.x + (item.width / 2),
        centerY: item.y + (item.height / 2)
      }))
    }

    return results
  }

  /**
   * Clear all caches (call when PDF changes)
   */
  clearCache() {
    this.textContentCache.clear()
    this.coordinateCache.clear()
  }

  /**
   * Clear cache for specific page
   * @param {number} pageIndex - Page index to clear
   */
  clearPageCache(pageIndex) {
    const cacheKey = `page_${pageIndex}`
    this.textContentCache.delete(cacheKey)
  }
}

// Create a singleton instance for use across the application
export const pdfTextSearcher = new PDFTextSearcher()

/**
 * Utility function to search for text coordinates (backward compatibility)
 * @param {Object} pdfPage - PDF.js page object
 * @param {string} searchText - Text to search for
 * @param {number} pageIndex - Page index for caching
 * @param {boolean} caseSensitive - Whether search should be case sensitive
 * @returns {Promise<Array>} Array of coordinate objects
 */
export const searchTextCoordinates = async (pdfPage, searchText, pageIndex, caseSensitive = false) => {
  return pdfTextSearcher.searchTextCoordinates(pdfPage, searchText, pageIndex, caseSensitive)
}
