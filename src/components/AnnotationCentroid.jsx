/**
 * Annotation Centroid Calculation Utilities
 * 
 * This module provides functionality to calculate centroids for different types
 * of annotations (rectangles and polygons) for proximity-based room sorting.
 */

/**
 * Calculate the centroid of a rectangle annotation
 * @param {Object} annotation - Rectangle annotation object
 * @param {number} annotation.x - X coordinate of top-left corner
 * @param {number} annotation.y - Y coordinate of top-left corner
 * @param {number} annotation.width - Width of the rectangle
 * @param {number} annotation.height - Height of the rectangle
 * @returns {Object} Centroid coordinates {x, y}
 */
export const calculateRectangleCentroid = (annotation) => {
  if (!annotation || annotation.type !== 'rectangle') {
    throw new Error('Invalid rectangle annotation')
  }

  const { x, y, width, height } = annotation

  if (typeof x !== 'number' || typeof y !== 'number' || 
      typeof width !== 'number' || typeof height !== 'number') {
    throw new Error('Invalid rectangle coordinates')
  }

  return {
    x: x + (width / 2),
    y: y + (height / 2)
  }
}

/**
 * Calculate the centroid of a polygon annotation using the geometric centroid formula
 * @param {Object} annotation - Polygon annotation object
 * @param {Array} annotation.points - Array of points [{x, y}, ...]
 * @returns {Object} Centroid coordinates {x, y}
 */
export const calculatePolygonCentroid = (annotation) => {
  if (!annotation || annotation.type !== 'polygon') {
    throw new Error('Invalid polygon annotation')
  }

  const { points } = annotation

  if (!Array.isArray(points) || points.length === 0) {
    throw new Error('Invalid polygon points')
  }

  // For a simple polygon, we can use the arithmetic mean of all vertices
  // This works well for most practical cases
  if (points.length === 1) {
    return { x: points[0].x, y: points[0].y }
  }

  if (points.length === 2) {
    return {
      x: (points[0].x + points[1].x) / 2,
      y: (points[0].y + points[1].y) / 2
    }
  }

  // For polygons with 3+ points, use the geometric centroid formula
  return calculateGeometricCentroid(points)
}

/**
 * Calculate the geometric centroid of a polygon using the shoelace formula
 * @param {Array} points - Array of points [{x, y}, ...]
 * @returns {Object} Centroid coordinates {x, y}
 */
const calculateGeometricCentroid = (points) => {
  const n = points.length
  
  // Calculate the signed area of the polygon
  let area = 0
  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n
    area += points[i].x * points[j].y
    area -= points[j].x * points[i].y
  }
  area /= 2

  // If area is very small or zero, fall back to arithmetic mean
  if (Math.abs(area) < 1e-10) {
    return calculateArithmeticCentroid(points)
  }

  // Calculate centroid coordinates
  let cx = 0
  let cy = 0
  
  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n
    const factor = points[i].x * points[j].y - points[j].x * points[i].y
    cx += (points[i].x + points[j].x) * factor
    cy += (points[i].y + points[j].y) * factor
  }

  cx /= (6 * area)
  cy /= (6 * area)

  return { x: cx, y: cy }
}

/**
 * Calculate the arithmetic centroid (simple average) of polygon points
 * @param {Array} points - Array of points [{x, y}, ...]
 * @returns {Object} Centroid coordinates {x, y}
 */
const calculateArithmeticCentroid = (points) => {
  const sumX = points.reduce((sum, point) => sum + point.x, 0)
  const sumY = points.reduce((sum, point) => sum + point.y, 0)
  
  return {
    x: sumX / points.length,
    y: sumY / points.length
  }
}

/**
 * Calculate the centroid of any annotation type
 * @param {Object} annotation - Annotation object (rectangle or polygon)
 * @returns {Object} Centroid coordinates {x, y}
 */
export const calculateAnnotationCentroid = (annotation) => {
  if (!annotation || !annotation.type) {
    throw new Error('Invalid annotation object')
  }

  switch (annotation.type) {
    case 'rectangle':
      return calculateRectangleCentroid(annotation)
    case 'polygon':
      return calculatePolygonCentroid(annotation)
    default:
      throw new Error(`Unsupported annotation type: ${annotation.type}`)
  }
}

/**
 * Calculate centroids for multiple annotations
 * @param {Array} annotations - Array of annotation objects
 * @returns {Array} Array of objects with annotation and centroid {annotation, centroid}
 */
export const calculateMultipleCentroids = (annotations) => {
  if (!Array.isArray(annotations)) {
    return []
  }

  return annotations.map(annotation => {
    try {
      const centroid = calculateAnnotationCentroid(annotation)
      return { annotation, centroid }
    } catch (error) {
      console.warn('Error calculating centroid for annotation:', annotation.id, error.message)
      return { annotation, centroid: null }
    }
  }).filter(item => item.centroid !== null)
}

/**
 * Get the bounding box of an annotation
 * @param {Object} annotation - Annotation object
 * @returns {Object} Bounding box {minX, minY, maxX, maxY, width, height}
 */
export const getAnnotationBounds = (annotation) => {
  if (!annotation || !annotation.type) {
    throw new Error('Invalid annotation object')
  }

  switch (annotation.type) {
    case 'rectangle':
      return {
        minX: annotation.x,
        minY: annotation.y,
        maxX: annotation.x + annotation.width,
        maxY: annotation.y + annotation.height,
        width: annotation.width,
        height: annotation.height
      }
    case 'polygon':
      if (!annotation.points || annotation.points.length === 0) {
        throw new Error('Invalid polygon points')
      }
      
      const xs = annotation.points.map(p => p.x)
      const ys = annotation.points.map(p => p.y)
      const minX = Math.min(...xs)
      const minY = Math.min(...ys)
      const maxX = Math.max(...xs)
      const maxY = Math.max(...ys)
      
      return {
        minX,
        minY,
        maxX,
        maxY,
        width: maxX - minX,
        height: maxY - minY
      }
    default:
      throw new Error(`Unsupported annotation type: ${annotation.type}`)
  }
}

/**
 * Test centroid calculations with sample data
 * @returns {Object} Test results
 */
export const testCentroidCalculations = () => {
  const testAnnotations = [
    {
      id: 'rect1',
      type: 'rectangle',
      x: 100,
      y: 200,
      width: 50,
      height: 30
    },
    {
      id: 'poly1',
      type: 'polygon',
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 }
      ]
    },
    {
      id: 'poly2',
      type: 'polygon',
      points: [
        { x: 0, y: 0 },
        { x: 50, y: 0 },
        { x: 25, y: 50 }
      ]
    }
  ]

  const results = testAnnotations.map(annotation => ({
    annotation: annotation.id,
    type: annotation.type,
    centroid: calculateAnnotationCentroid(annotation),
    bounds: getAnnotationBounds(annotation)
  }))

  console.log('Centroid Calculation Test Results:', results)
  return results
}
