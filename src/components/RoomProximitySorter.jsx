/**
 * Room Proximity Sorting Utilities
 * 
 * This module provides functionality to sort rooms by proximity to annotation centroids.
 * It integrates room code extraction, PDF text search, coordinate transformation,
 * and distance calculation to provide proximity-based room sorting.
 */

import { extractRoomCode } from './RoomCodeExtractor'
import { calculateAnnotationCentroid } from './AnnotationCentroid'
import { createCoordinateTransformer, validateCoordinates } from './CoordinateTransform'
import { pdfTextSearcher } from './PDFTextSearch'

/**
 * Room Proximity Sorter class
 */
export class RoomProximitySorter {
  constructor() {
    this.roomCoordinateCache = new Map()
    this.coordinateTransformer = null
    this.currentPdfPage = null
    this.currentPageIndex = null
  }

  /**
   * Initialize the sorter with PDF page and dimensions
   * @param {Object} pdfPage - PDF.js page object
   * @param {number} pageIndex - Current page index
   * @param {Object} pdfDimensions - PDF dimensions {width, height}
   * @param {Object} canvasDimensions - Canvas dimensions {width, height}
   */
  initialize(pdfPage, pageIndex, pdfDimensions, canvasDimensions) {
    this.currentPdfPage = pdfPage
    this.currentPageIndex = pageIndex
    this.coordinateTransformer = createCoordinateTransformer(pdfDimensions, canvasDimensions)
    
    // Clear cache when page changes
    if (this.lastPageIndex !== pageIndex) {
      this.roomCoordinateCache.clear()
      this.lastPageIndex = pageIndex
    }
  }

  /**
   * Find coordinates for a room code in the current PDF page
   * @param {string} roomCode - Room code to search for
   * @returns {Promise<Object|null>} Room coordinates {x, y, centerX, centerY} or null if not found
   */
  async findRoomCoordinates(roomCode) {
    if (!roomCode || !this.currentPdfPage) {
      return null
    }

    // Check cache first
    const cacheKey = `${this.currentPageIndex}_${roomCode}`
    if (this.roomCoordinateCache.has(cacheKey)) {
      return this.roomCoordinateCache.get(cacheKey)
    }

    try {
      // Search for room code in PDF text
      const searchResults = await pdfTextSearcher.searchTextCoordinates(
        this.currentPdfPage,
        roomCode,
        this.currentPageIndex,
        false // case insensitive
      )

      if (searchResults.length === 0) {
        // Cache negative result
        this.roomCoordinateCache.set(cacheKey, null)
        return null
      }

      // Use the first match (or could implement logic to choose best match)
      const bestMatch = searchResults[0]
      const coordinates = {
        x: bestMatch.x,
        y: bestMatch.y,
        width: bestMatch.width,
        height: bestMatch.height,
        centerX: bestMatch.centerX,
        centerY: bestMatch.centerY,
        text: bestMatch.text
      }

      // Cache the result
      this.roomCoordinateCache.set(cacheKey, coordinates)
      return coordinates
    } catch (error) {
      console.error('Error finding room coordinates:', error)
      this.roomCoordinateCache.set(cacheKey, null)
      return null
    }
  }

  /**
   * Calculate distance between annotation centroid and room coordinates
   * @param {Object} annotationCentroid - Annotation centroid in canvas coordinates
   * @param {Object} roomCoordinates - Room coordinates from PDF text search
   * @returns {number} Distance in PDF coordinate units
   */
  calculateDistance(annotationCentroid, roomCoordinates) {
    if (!this.coordinateTransformer || !annotationCentroid || !roomCoordinates) {
      return Infinity
    }

    // Validate coordinates
    if (!validateCoordinates.isValid(annotationCentroid) || 
        !validateCoordinates.isValid(roomCoordinates)) {
      return Infinity
    }

    // Use the center point of the room text for distance calculation
    const roomCenter = {
      x: roomCoordinates.centerX,
      y: roomCoordinates.centerY
    }

    return this.coordinateTransformer.calculateAnnotationToRoomDistance(
      annotationCentroid,
      roomCenter
    )
  }

  /**
   * Sort rooms by proximity to an annotation
   * @param {Object} annotation - Annotation object
   * @param {Array} roomNames - Array of room names to sort
   * @returns {Promise<Array>} Sorted array of room objects with distances
   */
  async sortRoomsByProximity(annotation, roomNames) {
    if (!annotation || !Array.isArray(roomNames) || roomNames.length === 0) {
      return []
    }

    try {
      // Calculate annotation centroid
      const annotationCentroid = calculateAnnotationCentroid(annotation)
      
      // Process each room
      const roomsWithDistances = await Promise.all(
        roomNames.map(async (roomName) => {
          // Extract room code from room name
          const roomCode = extractRoomCode(roomName)
          
          if (!roomCode) {
            return {
              roomName,
              roomCode: null,
              coordinates: null,
              distance: Infinity,
              found: false
            }
          }

          // Find room coordinates in PDF
          const coordinates = await this.findRoomCoordinates(roomCode)
          
          if (!coordinates) {
            return {
              roomName,
              roomCode,
              coordinates: null,
              distance: Infinity,
              found: false
            }
          }

          // Calculate distance
          const distance = this.calculateDistance(annotationCentroid, coordinates)
          
          return {
            roomName,
            roomCode,
            coordinates,
            distance,
            found: true
          }
        })
      )

      // Sort by distance (ascending - nearest first)
      const sortedRooms = roomsWithDistances.sort((a, b) => {
        // Prioritize found rooms over not found
        if (a.found && !b.found) return -1
        if (!a.found && b.found) return 1
        
        // Then sort by distance
        return a.distance - b.distance
      })

      return sortedRooms
    } catch (error) {
      console.error('Error sorting rooms by proximity:', error)
      // Return original room names as fallback
      return roomNames.map(roomName => ({
        roomName,
        roomCode: null,
        coordinates: null,
        distance: Infinity,
        found: false
      }))
    }
  }

  /**
   * Get sorted room names only (for backward compatibility)
   * @param {Object} annotation - Annotation object
   * @param {Array} roomNames - Array of room names to sort
   * @returns {Promise<Array>} Sorted array of room names
   */
  async getSortedRoomNames(annotation, roomNames) {
    const sortedRooms = await this.sortRoomsByProximity(annotation, roomNames)
    return sortedRooms.map(room => room.roomName)
  }

  /**
   * Clear all caches
   */
  clearCache() {
    this.roomCoordinateCache.clear()
    if (pdfTextSearcher) {
      pdfTextSearcher.clearCache()
    }
  }

  /**
   * Get cache statistics for debugging
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      roomCoordinateCacheSize: this.roomCoordinateCache.size,
      currentPageIndex: this.currentPageIndex,
      hasCoordinateTransformer: !!this.coordinateTransformer,
      hasPdfPage: !!this.currentPdfPage
    }
  }

  /**
   * Test the proximity sorting with sample data
   * @param {Object} testAnnotation - Test annotation
   * @param {Array} testRoomNames - Test room names
   * @returns {Promise<Object>} Test results
   */
  async testProximitySorting(testAnnotation, testRoomNames) {
    console.log('Testing proximity sorting...')
    console.log('Annotation:', testAnnotation)
    console.log('Room names:', testRoomNames)
    
    const results = await this.sortRoomsByProximity(testAnnotation, testRoomNames)
    
    console.log('Sorting results:', results)
    console.log('Cache stats:', this.getCacheStats())
    
    return {
      annotation: testAnnotation,
      roomNames: testRoomNames,
      sortedRooms: results,
      cacheStats: this.getCacheStats()
    }
  }
}

// Create a singleton instance for use across the application
export const roomProximitySorter = new RoomProximitySorter()

/**
 * Utility function to sort rooms by proximity (backward compatibility)
 * @param {Object} annotation - Annotation object
 * @param {Array} roomNames - Array of room names to sort
 * @param {Object} pdfPage - PDF.js page object
 * @param {number} pageIndex - Current page index
 * @param {Object} pdfDimensions - PDF dimensions
 * @param {Object} canvasDimensions - Canvas dimensions
 * @returns {Promise<Array>} Sorted array of room names
 */
export const sortRoomsByProximity = async (
  annotation,
  roomNames,
  pdfPage,
  pageIndex,
  pdfDimensions,
  canvasDimensions
) => {
  roomProximitySorter.initialize(pdfPage, pageIndex, pdfDimensions, canvasDimensions)
  return roomProximitySorter.getSortedRoomNames(annotation, roomNames)
}
