/**
 * Room Proximity Sorting Utilities
 * 
 * This module provides functionality to sort rooms by proximity to annotation centroids.
 * It integrates room code extraction, PDF text search, coordinate transformation,
 * and distance calculation to provide proximity-based room sorting.
 */

import { extractRoomCode } from './RoomCodeExtractor'
import { calculateAnnotationCentroid } from './AnnotationCentroid'
import { createCoordinateTransformer, validateCoordinates } from './CoordinateTransform'
import { pdfTextSearcher } from './PDFTextSearch'

/**
 * Room Proximity Sorter class
 */
export class RoomProximitySorter {
  constructor() {
    this.roomCoordinateCache = new Map()
    this.coordinateTransformer = null
    this.currentPdfPage = null
    this.currentPageIndex = null
  }

  /**
   * Initialize the sorter with PDF page and dimensions
   * @param {Object} pdfPage - PDF.js page object
   * @param {number} pageIndex - Current page index
   * @param {Object} pdfDimensions - PDF dimensions {width, height}
   * @param {Object} canvasDimensions - Canvas dimensions {width, height}
   */
  initialize(pdfPage, pageIndex, pdfDimensions, canvasDimensions) {
    console.log('🔧 RoomProximitySorter: Initializing with:', {
      hasPdfPage: !!pdfPage,
      pageIndex,
      pdfDimensions,
      canvasDimensions,
      lastPageIndex: this.lastPageIndex
    })

    this.currentPdfPage = pdfPage
    this.currentPageIndex = pageIndex
    this.coordinateTransformer = createCoordinateTransformer(pdfDimensions, canvasDimensions)

    // Clear cache when page changes
    if (this.lastPageIndex !== pageIndex) {
      console.log('🗑️ RoomProximitySorter: Clearing cache due to page change')
      this.roomCoordinateCache.clear()
      this.lastPageIndex = pageIndex
    }

    console.log('✅ RoomProximitySorter: Initialization complete')
  }

  /**
   * Find coordinates for a room code in the current PDF page
   * @param {string} roomCode - Room code to search for
   * @returns {Promise<Object|null>} Room coordinates {x, y, centerX, centerY} or null if not found
   */
  async findRoomCoordinates(roomCode) {
    console.log(`🔍 RoomProximitySorter: findRoomCoordinates called for "${roomCode}"`)

    if (!roomCode || !this.currentPdfPage) {
      console.log(`❌ RoomProximitySorter: Invalid parameters - roomCode: "${roomCode}", hasPdfPage: ${!!this.currentPdfPage}`)
      return null
    }

    // Check cache first
    const cacheKey = `${this.currentPageIndex}_${roomCode}`
    console.log(`🗄️ RoomProximitySorter: Checking cache for key: "${cacheKey}"`)

    if (this.roomCoordinateCache.has(cacheKey)) {
      const cachedResult = this.roomCoordinateCache.get(cacheKey)
      console.log(`✅ RoomProximitySorter: Found cached result for "${roomCode}":`, cachedResult)
      return cachedResult
    }

    try {
      console.log(`🔎 RoomProximitySorter: Searching PDF for room code "${roomCode}"`)

      // Search for room code in PDF text
      const searchResults = await pdfTextSearcher.searchTextCoordinates(
        this.currentPdfPage,
        roomCode,
        this.currentPageIndex,
        false // case insensitive
      )

      console.log(`📊 RoomProximitySorter: Search results for "${roomCode}":`, {
        resultsCount: searchResults.length,
        results: searchResults
      })

      if (searchResults.length === 0) {
        console.log(`❌ RoomProximitySorter: No search results found for "${roomCode}"`)
        // Cache negative result
        this.roomCoordinateCache.set(cacheKey, null)
        return null
      }

      // Use the first match (or could implement logic to choose best match)
      const bestMatch = searchResults[0]
      const coordinates = {
        x: bestMatch.x,
        y: bestMatch.y,
        width: bestMatch.width,
        height: bestMatch.height,
        centerX: bestMatch.centerX,
        centerY: bestMatch.centerY,
        text: bestMatch.text
      }

      console.log(`✅ RoomProximitySorter: Found coordinates for "${roomCode}":`, coordinates)

      // Cache the result
      this.roomCoordinateCache.set(cacheKey, coordinates)
      return coordinates
    } catch (error) {
      console.error(`❌ RoomProximitySorter: Error finding room coordinates for "${roomCode}":`, error)
      this.roomCoordinateCache.set(cacheKey, null)
      return null
    }
  }

  /**
   * Calculate distance between annotation centroid and room coordinates
   * @param {Object} annotationCentroid - Annotation centroid in canvas coordinates
   * @param {Object} roomCoordinates - Room coordinates from PDF text search
   * @returns {number} Distance in PDF coordinate units
   */
  calculateDistance(annotationCentroid, roomCoordinates) {
    console.log(`📏 RoomProximitySorter: calculateDistance called with:`, {
      annotationCentroid,
      roomCoordinates: roomCoordinates ? {
        x: roomCoordinates.x,
        y: roomCoordinates.y,
        centerX: roomCoordinates.centerX,
        centerY: roomCoordinates.centerY,
        text: roomCoordinates.text
      } : null
    })

    if (!this.coordinateTransformer || !annotationCentroid || !roomCoordinates) {
      console.log(`❌ RoomProximitySorter: Missing required parameters for distance calculation`)
      return Infinity
    }

    // Validate coordinates
    if (!validateCoordinates.isValid(annotationCentroid)) {
      console.log(`❌ RoomProximitySorter: Invalid annotation centroid:`, annotationCentroid)
      return Infinity
    }

    // For room coordinates, we need to check if centerX and centerY are valid
    const roomCenter = {
      x: roomCoordinates.centerX,
      y: roomCoordinates.centerY
    }

    if (!validateCoordinates.isValid(roomCenter)) {
      console.log(`❌ RoomProximitySorter: Invalid room center coordinates:`, roomCenter)
      return Infinity
    }

    console.log(`📏 RoomProximitySorter: Calculating distance between annotation centroid and room center:`, {
      annotationCentroid,
      roomCenter
    })

    const distance = this.coordinateTransformer.calculateAnnotationToRoomDistance(
      annotationCentroid,
      roomCenter
    )

    console.log(`📏 RoomProximitySorter: Calculated distance: ${distance}`)
    return distance
  }

  /**
   * Sort rooms by proximity to an annotation
   * @param {Object} annotation - Annotation object
   * @param {Array} roomNames - Array of room names to sort
   * @returns {Promise<Array>} Sorted array of room objects with distances
   */
  async sortRoomsByProximity(annotation, roomNames) {
    console.log('🎯 RoomProximitySorter: sortRoomsByProximity called with:', {
      annotation: annotation ? { id: annotation.id, type: annotation.type, x: annotation.x, y: annotation.y } : null,
      roomNamesCount: roomNames.length,
      firstFewRooms: roomNames.slice(0, 3)
    })

    if (!annotation || !Array.isArray(roomNames) || roomNames.length === 0) {
      console.log('❌ RoomProximitySorter: Invalid input parameters')
      return []
    }

    try {
      // Calculate annotation centroid
      console.log('📐 RoomProximitySorter: Calculating annotation centroid...')
      const annotationCentroid = calculateAnnotationCentroid(annotation)
      console.log('📐 RoomProximitySorter: Annotation centroid:', annotationCentroid)

      // Process each room
      console.log('🔄 RoomProximitySorter: Processing rooms...')
      const roomsWithDistances = await Promise.all(
        roomNames.map(async (roomName, index) => {
          console.log(`🏠 RoomProximitySorter: Processing room ${index + 1}/${roomNames.length}: "${roomName}"`)

          // Extract room code from room name
          const roomCode = extractRoomCode(roomName)
          console.log(`🔍 RoomProximitySorter: Extracted room code for "${roomName}": "${roomCode}"`)

          if (!roomCode) {
            console.log(`❌ RoomProximitySorter: No room code found for "${roomName}"`)
            return {
              roomName,
              roomCode: null,
              coordinates: null,
              distance: Infinity,
              found: false
            }
          }

          // Find room coordinates in PDF
          console.log(`📍 RoomProximitySorter: Finding coordinates for room code "${roomCode}"`)
          const coordinates = await this.findRoomCoordinates(roomCode)
          console.log(`📍 RoomProximitySorter: Coordinates for "${roomCode}":`, coordinates)

          if (!coordinates) {
            console.log(`❌ RoomProximitySorter: No coordinates found for room code "${roomCode}"`)
            return {
              roomName,
              roomCode,
              coordinates: null,
              distance: Infinity,
              found: false
            }
          }

          // Calculate distance
          console.log(`📏 RoomProximitySorter: Calculating distance for "${roomName}"`)
          const distance = this.calculateDistance(annotationCentroid, coordinates)
          console.log(`📏 RoomProximitySorter: Distance for "${roomName}": ${distance}`)

          return {
            roomName,
            roomCode,
            coordinates,
            distance,
            found: true
          }
        })
      )

      console.log('📊 RoomProximitySorter: All rooms processed, sorting by distance...')

      // Sort by distance (ascending - nearest first)
      const sortedRooms = roomsWithDistances.sort((a, b) => {
        // Prioritize found rooms over not found
        if (a.found && !b.found) return -1
        if (!a.found && b.found) return 1

        // Then sort by distance
        return a.distance - b.distance
      })

      console.log('✅ RoomProximitySorter: Sorting completed:', {
        totalRooms: sortedRooms.length,
        foundRooms: sortedRooms.filter(r => r.found).length,
        notFoundRooms: sortedRooms.filter(r => !r.found).length,
        topThree: sortedRooms.slice(0, 3).map(r => ({ name: r.roomName, distance: r.distance, found: r.found }))
      })

      return sortedRooms
    } catch (error) {
      console.error('❌ RoomProximitySorter: Error sorting rooms by proximity:', error)
      // Return original room names as fallback
      return roomNames.map(roomName => ({
        roomName,
        roomCode: null,
        coordinates: null,
        distance: Infinity,
        found: false
      }))
    }
  }

  /**
   * Get sorted room names only (for backward compatibility)
   * @param {Object} annotation - Annotation object
   * @param {Array} roomNames - Array of room names to sort
   * @returns {Promise<Array>} Sorted array of room names
   */
  async getSortedRoomNames(annotation, roomNames) {
    console.log('📋 RoomProximitySorter: getSortedRoomNames called with:', {
      annotation: annotation ? { id: annotation.id, type: annotation.type } : null,
      roomNamesCount: roomNames.length
    })

    try {
      const sortedRooms = await this.sortRoomsByProximity(annotation, roomNames)
      const sortedNames = sortedRooms.map(room => room.roomName)

      console.log('📋 RoomProximitySorter: getSortedRoomNames completed:', {
        inputCount: roomNames.length,
        outputCount: sortedNames.length
      })

      return sortedNames
    } catch (error) {
      console.error('❌ RoomProximitySorter: Error in getSortedRoomNames:', error)
      throw error
    }
  }

  /**
   * Clear all caches
   */
  clearCache() {
    this.roomCoordinateCache.clear()
    if (pdfTextSearcher) {
      pdfTextSearcher.clearCache()
    }
  }

  /**
   * Get cache statistics for debugging
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      roomCoordinateCacheSize: this.roomCoordinateCache.size,
      currentPageIndex: this.currentPageIndex,
      hasCoordinateTransformer: !!this.coordinateTransformer,
      hasPdfPage: !!this.currentPdfPage
    }
  }

  /**
   * Debug method to test basic functionality
   * @returns {Promise<Object>} Debug results
   */
  async debugBasicFunctionality() {
    console.log('🐛 RoomProximitySorter: Starting debug test...')

    const stats = this.getCacheStats()
    console.log('📊 Current stats:', stats)

    if (!this.currentPdfPage) {
      console.log('❌ No PDF page available for testing')
      return { error: 'No PDF page available' }
    }

    try {
      // Test text extraction
      console.log('📄 Testing text extraction...')
      const textItems = await pdfTextSearcher.extractTextContent(this.currentPdfPage, this.currentPageIndex)
      console.log(`📄 Extracted ${textItems.length} text items`)

      // Show sample text items
      const sampleTexts = textItems.slice(0, 10).map(item => item.str)
      console.log('📄 Sample texts:', sampleTexts)

      return {
        success: true,
        textItemsCount: textItems.length,
        sampleTexts,
        stats
      }
    } catch (error) {
      console.error('❌ Debug test failed:', error)
      return { error: error.message }
    }
  }

  /**
   * Test the proximity sorting with sample data
   * @param {Object} testAnnotation - Test annotation
   * @param {Array} testRoomNames - Test room names
   * @returns {Promise<Object>} Test results
   */
  async testProximitySorting(testAnnotation, testRoomNames) {
    console.log('Testing proximity sorting...')
    console.log('Annotation:', testAnnotation)
    console.log('Room names:', testRoomNames)
    
    const results = await this.sortRoomsByProximity(testAnnotation, testRoomNames)
    
    console.log('Sorting results:', results)
    console.log('Cache stats:', this.getCacheStats())
    
    return {
      annotation: testAnnotation,
      roomNames: testRoomNames,
      sortedRooms: results,
      cacheStats: this.getCacheStats()
    }
  }
}

// Create a singleton instance for use across the application
export const roomProximitySorter = new RoomProximitySorter()

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  window.roomProximitySorter = roomProximitySorter
}

/**
 * Utility function to sort rooms by proximity (backward compatibility)
 * @param {Object} annotation - Annotation object
 * @param {Array} roomNames - Array of room names to sort
 * @param {Object} pdfPage - PDF.js page object
 * @param {number} pageIndex - Current page index
 * @param {Object} pdfDimensions - PDF dimensions
 * @param {Object} canvasDimensions - Canvas dimensions
 * @returns {Promise<Array>} Sorted array of room names
 */
export const sortRoomsByProximity = async (
  annotation,
  roomNames,
  pdfPage,
  pageIndex,
  pdfDimensions,
  canvasDimensions
) => {
  roomProximitySorter.initialize(pdfPage, pageIndex, pdfDimensions, canvasDimensions)
  return roomProximitySorter.getSortedRoomNames(annotation, roomNames)
}
