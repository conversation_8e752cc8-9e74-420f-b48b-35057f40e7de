import { useState, useRef, useCallback } from 'react'
import * as pdfjsLib from 'pdfjs-dist'

// Set up PDF.js worker - using local build files
pdfjsLib.GlobalWorkerOptions.workerSrc = '/PDF_BUILD/build/pdf.worker.mjs'

export const usePDFHandler = () => {
  const [pdfFiles, setPdfFiles] = useState([]) // Array of PDF files
  const [currentPdfIndex, setCurrentPdfIndex] = useState(0) // Current PDF being viewed
  const [pdfPages, setPdfPages] = useState([]) // Pages of current PDF
  const [currentPageIndex, setCurrentPageIndex] = useState(0) // Current page of current PDF
  const originalPdfDimensions = useRef({ width: 0, height: 0 })
  const allPdfData = useRef([]) // Store all PDF data and dimensions

  // Handle multiple PDF file upload
  const handleFileUpload = useCallback(async (event) => {
    const files = Array.from(event.target.files)
    if (files.length === 0) return

    // Validate all files are PDFs
    const invalidFiles = files.filter(file => file.type !== 'application/pdf')
    if (invalidFiles.length > 0) {
      alert('Please select only PDF files')
      return
    }

    if (files.length > 100) {
      alert('Maximum 100 PDF files allowed')
      return
    }

    try {
      const newPdfData = []

      for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
        const file = files[fileIndex]
        const arrayBuffer = await file.arrayBuffer()
        const pdf = await pdfjsLib.getDocument(arrayBuffer).promise
        const pages = []
        let pdfDimensions = { width: 0, height: 0 }

        for (let i = 1; i <= pdf.numPages; i++) {
          const page = await pdf.getPage(i)
          const viewport = page.getViewport({ scale: 1 }) // Get original PDF dimensions

          // Store original PDF dimensions (at scale 1) for coordinate mapping
          if (i === 1) { // Store dimensions from first page
            pdfDimensions = {
              width: viewport.width,
              height: viewport.height
            }
          }

          // Create high-resolution viewport for rendering
          const renderViewport = page.getViewport({ scale: 300 / 72 }) // 300 DPI

          const canvas = document.createElement('canvas')
          const context = canvas.getContext('2d')
          canvas.width = renderViewport.width
          canvas.height = renderViewport.height

          const renderContext = {
            canvasContext: context,
            viewport: renderViewport
          }

          await page.render(renderContext).promise

          pages.push({
            canvas: canvas,
            imageData: canvas.toDataURL('image/png'),
            width: renderViewport.width,
            height: renderViewport.height,
            // Store the PDF.js page object for text extraction
            pdfPage: page
          })
        }

        newPdfData.push({
          file: file,
          pages: pages,
          dimensions: pdfDimensions,
          name: file.name
        })
      }

      // Update state with new PDF data
      setPdfFiles(prev => [...prev, ...files])
      allPdfData.current = [...allPdfData.current, ...newPdfData]

      // If this is the first upload, set current PDF and initialize
      if (allPdfData.current.length === newPdfData.length) {
        setCurrentPdfIndex(0)
        setPdfPages(newPdfData[0].pages)
        originalPdfDimensions.current = newPdfData[0].dimensions
        setCurrentPageIndex(0)
      }

    } catch (error) {
      console.error('Error processing PDF files:', error)
      alert('Error processing PDF files')
    }
  }, [])

  // Switch to a different PDF
  const switchToPdf = useCallback((pdfIndex) => {
    if (pdfIndex < 0 || pdfIndex >= allPdfData.current.length) return

    setCurrentPdfIndex(pdfIndex)
    setPdfPages(allPdfData.current[pdfIndex].pages)
    originalPdfDimensions.current = allPdfData.current[pdfIndex].dimensions
    setCurrentPageIndex(0)
  }, [])

  // Convert canvas coordinates to PDF coordinates (for export)
  const canvasToPdfCoordinates = useCallback((canvasX, canvasY) => {
    if (pdfPages.length === 0) return { x: 0, y: 0 }

    const currentPage = pdfPages[currentPageIndex]
    const scaleX = originalPdfDimensions.current.width / currentPage.width
    const scaleY = originalPdfDimensions.current.height / currentPage.height

    return {
      x: canvasX * scaleX,
      y: canvasY * scaleY
    }
  }, [pdfPages, currentPageIndex])

  return {
    // State
    pdfFiles,
    currentPdfIndex,
    pdfPages,
    currentPageIndex,
    originalPdfDimensions,
    allPdfData,
    
    // Actions
    handleFileUpload,
    switchToPdf,
    setCurrentPageIndex,
    canvasToPdfCoordinates
  }
}
